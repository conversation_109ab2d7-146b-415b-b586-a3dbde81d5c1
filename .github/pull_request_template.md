### `General`

> ✏️ Mark the necessary items without changing the structure of the PR template.

- [ ] Pull request template structure not broken

### `Type`

> ℹ️ What types of changes does your code introduce?

> 👉 _Put an `x` in the boxes that apply_

- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] This change requires a documentation update

### `Checklist`

> ℹ️ Check all checkboxes - this will indicate that you have done everything in accordance with the rules in [CONTRIBUTING](contributing.md).

> 👉 _Put an `x` in the boxes that apply._

- [ ] My code follows the style guidelines of this project
- [ ] Is the code format correct
- [ ] Is the git submission information standard?
- [ ] My code follows the style guidelines of this project
- [ ] I have performed a self-review of my own code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes
- [ ] Any dependent changes have been merged and published in downstream modules
