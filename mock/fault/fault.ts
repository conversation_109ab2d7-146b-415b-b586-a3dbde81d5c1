import { MockMethod } from 'vite-plugin-mock';
import { resultSuccess, resultPageSuccess } from '../_util';

export default [
  {
    url: '/V2/fault/selectFaultPreset',
    timeout: 100,
    method: 'get',
    response: () => {
      return resultSuccess([
        {
          title: '所有故障',
          key: 'all',
          icon: 'ant-design:home-outlined',
        },
        {
          title: '故障报表',
          selectable: false,
          key: 'report',
          icon: 'majesticons:file-report-line',
          children: [
            {
              title: '2024年所有故障',
              key: 'ReportData-0-0-0',
              children: [
                {
                  title: '2024年H1所有故障',
                  key: 'ReportData-0-0-0-0',
                  children: [
                    {
                      title: '2024年Q1所有故障',
                      key: 'ReportData-0-0-0-0-0',
                      children: [
                        {
                          title: '2024年1月所有故障',
                          key: 'ReportData-0-0-0-0-0-0',
                        },
                        {
                          title: '2024年2月所有故障',
                          key: 'ReportData-0-0-0-0-0-1',
                        },
                        {
                          title: '2024年3月所有故障',
                          key: 'ReportData-0-0-0-0-0-2',
                        },
                      ],
                    },
                    {
                      title: '2024年Q2所有故障',
                      key: 'ReportData-0-0-0-0-1',
                      children: [
                        {
                          title: '2024年4月所有故障',
                          key: 'ReportData-0-0-0-0-1-0',
                        },
                        {
                          title: '2024年5月所有故障',
                          key: 'ReportData-0-0-0-0-1-1',
                        },
                        {
                          title: '2024年6月所有故障',
                          key: 'ReportData-0-0-0-0-1-2',
                        },
                      ],
                    },
                  ],
                },
                { title: '2024年H2所有故障', key: 'ReportData-0-0-0-1' },
              ],
            },
            { title: '2024年定级故障', key: 'ReportData-0-0-1' },
          ],
        },
      ]);
    },
  },
] as MockMethod[];
