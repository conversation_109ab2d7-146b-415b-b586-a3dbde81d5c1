import { MockMethod } from 'vite-plugin-mock';
import { resultSuccess, resultPageSuccess } from '../_util';

const ticketList = (() => {
  const ticketListData = [
    {
      startTime: '2023-11-20 23:35:24',
      enterTime: '2023-11-20 23:35:24',
      responseTime: null,
      endTime: null,
      ticketID: '1700494524000046',
      stage: '处理中',
      owner: [
        {
          name: '刘智敏',
          open_id: '',
        },
        {
          name: '董富欣',
          open_id: '',
        },
        {
          name: '蒋海波',
          open_id: '',
        },
        {
          name: '陈健祥',
          open_id: '',
        },
        {
          name: '王恒岩',
          open_id: '',
        },
        {
          name: '皮文盛',
          open_id: '',
        },
        {
          name: '黄玮文',
          open_id: '',
        },
        {
          name: '黄卫宁',
          open_id: '',
        },
      ],
      businessOwner: [
        {
          name: '刘智敏',
          open_id: '',
        },
        {
          name: '董富欣',
          open_id: '',
        },
      ],
      devOwner: [
        {
          name: '蒋海波',
          open_id: '',
        },
        {
          name: '陈健祥',
          open_id: '',
        },
      ],
      clientIOSOwner: [
        {
          name: '王恒岩',
          open_id: '',
        },
        {
          name: '皮文盛',
          open_id: '',
        },
      ],
      clientAndroidOwner: [
        {
          name: '黄玮文',
          open_id: '',
        },
        {
          name: '黄卫宁',
          open_id: '',
        },
      ],
      problemDescription: '工单测试工单测试工单测试工单测试工单测试',
      userTtid: ['123456', '6554545'],
      attachments: null,
      apps: ['TT语音', '欢游'],
      appVersion: [],
      osType: [],
      mobileType: [],
      hasFeedback: null,
      feedbackPerson: 'admin',
      hasWorkingHours: null,
      severityLevel: null,
      functionType: ['首页', '扩列聊天', '小纸条'],
      firstLevelCategory: '首页',
      secondLevelCategory: '扩列聊天',
      thirdLevelCategory: '小纸条',
      reason: null,
      respondent: null,
      devProcessor: null,
      cause: null,
      hasClientRelease: null,
      changeLogs: [
        {
          time: '2023-11-20 23:35:24',
          content: '创建了工单',
          operator: 'admin',
        },
      ],
    },
    {
      startTime: null,
      enterTime: '2023-11-20 23:34:25',
      responseTime: null,
      endTime: null,
      ticketID: '1700494465000045',
      stage: '待处理',
      owner: [
        {
          name: '刘智敏',
          open_id: '',
        },
        {
          name: '董富欣',
          open_id: '',
        },
        {
          name: '蒋海波',
          open_id: '',
        },
        {
          name: '陈健祥',
          open_id: '',
        },
        {
          name: '王恒岩',
          open_id: '',
        },
        {
          name: '皮文盛',
          open_id: '',
        },
        {
          name: '黄玮文',
          open_id: '',
        },
        {
          name: '黄卫宁',
          open_id: '',
        },
      ],
      businessOwner: [
        {
          name: '刘智敏',
          open_id: '',
        },
        {
          name: '董富欣',
          open_id: '',
        },
      ],
      devOwner: [
        {
          name: '蒋海波',
          open_id: '',
        },
        {
          name: '陈健祥',
          open_id: '',
        },
      ],
      clientIOSOwner: [
        {
          name: '王恒岩',
          open_id: '',
        },
        {
          name: '皮文盛',
          open_id: '',
        },
      ],
      clientAndroidOwner: [
        {
          name: '黄玮文',
          open_id: '',
        },
        {
          name: '黄卫宁',
          open_id: '',
        },
      ],
      problemDescription: '123456',
      userTtid: [],
      attachments: null,
      apps: [],
      appVersion: [],
      osType: [],
      mobileType: [],
      hasFeedback: null,
      feedbackPerson: 'admin',
      hasWorkingHours: null,
      severityLevel: null,
      functionType: ['首页', '扩列聊天', '小纸条'],
      firstLevelCategory: '首页',
      secondLevelCategory: '扩列聊天',
      thirdLevelCategory: '小纸条',
      reason: null,
      respondent: null,
      devProcessor: null,
      cause: null,
      hasClientRelease: null,
      changeLogs: [
        {
          time: '2023-11-20 23:34:25',
          content: '创建了工单',
          operator: 'admin',
        },
      ],
    },
    {
      startTime: null,
      enterTime: '2023-11-20 23:30:56',
      responseTime: null,
      endTime: null,
      ticketID: '1700494256000044',
      stage: '待处理',
      owner: [
        {
          name: '刘智敏',
          open_id: '',
        },
        {
          name: '董富欣',
          open_id: '',
        },
        {
          name: '蒋海波',
          open_id: '',
        },
        {
          name: '陈健祥',
          open_id: '',
        },
        {
          name: '王恒岩',
          open_id: '',
        },
        {
          name: '皮文盛',
          open_id: '',
        },
        {
          name: '黄玮文',
          open_id: '',
        },
        {
          name: '黄卫宁',
          open_id: '',
        },
      ],
      businessOwner: [
        {
          name: '刘智敏',
          open_id: '',
        },
        {
          name: '董富欣',
          open_id: '',
        },
      ],
      devOwner: [
        {
          name: '蒋海波',
          open_id: '',
        },
        {
          name: '陈健祥',
          open_id: '',
        },
      ],
      clientIOSOwner: [
        {
          name: '王恒岩',
          open_id: '',
        },
        {
          name: '皮文盛',
          open_id: '',
        },
      ],
      clientAndroidOwner: [
        {
          name: '黄玮文',
          open_id: '',
        },
        {
          name: '黄卫宁',
          open_id: '',
        },
      ],
      problemDescription: '卡片联动测试',
      userTtid: ['123456'],
      attachments: null,
      apps: ['TT语音', '欢游'],
      appVersion: ['6.24.0'],
      osType: ['Android'],
      mobileType: ['小米10'],
      hasFeedback: null,
      feedbackPerson: 'admin',
      hasWorkingHours: null,
      severityLevel: null,
      functionType: ['首页', '扩列聊天', '小纸条'],
      firstLevelCategory: '首页',
      secondLevelCategory: '扩列聊天',
      thirdLevelCategory: '小纸条',
      reason: null,
      respondent: null,
      devProcessor: null,
      cause: null,
      hasClientRelease: null,
      changeLogs: [
        {
          time: '2023-11-20 23:30:56',
          content: '创建了工单',
          operator: 'admin',
        },
      ],
    },
    {
      startTime: null,
      enterTime: '2023-11-20 23:22:17',
      responseTime: null,
      endTime: null,
      ticketID: '1700493737000043',
      stage: '待处理',
      owner: [
        {
          name: '刘智敏',
          open_id: '',
        },
        {
          name: '董富欣',
          open_id: '',
        },
        {
          name: '蒋海波',
          open_id: '',
        },
        {
          name: '陈健祥',
          open_id: '',
        },
        {
          name: '王恒岩',
          open_id: '',
        },
        {
          name: '皮文盛',
          open_id: '',
        },
        {
          name: '黄玮文',
          open_id: '',
        },
        {
          name: '黄卫宁',
          open_id: '',
        },
      ],
      businessOwner: [
        {
          name: '刘智敏',
          open_id: '',
        },
        {
          name: '董富欣',
          open_id: '',
        },
      ],
      devOwner: [
        {
          name: '蒋海波',
          open_id: '',
        },
        {
          name: '陈健祥',
          open_id: '',
        },
      ],
      clientIOSOwner: [
        {
          name: '王恒岩',
          open_id: '',
        },
        {
          name: '皮文盛',
          open_id: '',
        },
      ],
      clientAndroidOwner: [
        {
          name: '黄玮文',
          open_id: '',
        },
        {
          name: '黄卫宁',
          open_id: '',
        },
      ],
      problemDescription: '卡片联动测试',
      userTtid: ['123456'],
      attachments: null,
      apps: ['TT语音', '欢游'],
      appVersion: ['6.24.0'],
      osType: ['Android'],
      mobileType: ['小米10'],
      hasFeedback: null,
      feedbackPerson: 'admin',
      hasWorkingHours: null,
      severityLevel: null,
      functionType: ['首页', '扩列聊天', '小纸条'],
      firstLevelCategory: '首页',
      secondLevelCategory: '扩列聊天',
      thirdLevelCategory: '小纸条',
      reason: null,
      respondent: null,
      devProcessor: null,
      cause: null,
      hasClientRelease: null,
      changeLogs: [
        {
          time: '2023-11-20 23:22:17',
          content: '创建了工单',
          operator: 'admin',
        },
      ],
    },
    {
      startTime: null,
      enterTime: '2023-11-20 23:19:02',
      responseTime: null,
      endTime: null,
      ticketID: '1700493542000042',
      stage: '待处理',
      owner: [
        {
          name: '刘智敏',
          open_id: '',
        },
        {
          name: '董富欣',
          open_id: '',
        },
        {
          name: '蒋海波',
          open_id: '',
        },
        {
          name: '陈健祥',
          open_id: '',
        },
        {
          name: '王恒岩',
          open_id: '',
        },
        {
          name: '皮文盛',
          open_id: '',
        },
        {
          name: '黄玮文',
          open_id: '',
        },
        {
          name: '黄卫宁',
          open_id: '',
        },
      ],
      businessOwner: [
        {
          name: '刘智敏',
          open_id: '',
        },
        {
          name: '董富欣',
          open_id: '',
        },
      ],
      devOwner: [
        {
          name: '蒋海波',
          open_id: '',
        },
        {
          name: '陈健祥',
          open_id: '',
        },
      ],
      clientIOSOwner: [
        {
          name: '王恒岩',
          open_id: '',
        },
        {
          name: '皮文盛',
          open_id: '',
        },
      ],
      clientAndroidOwner: [
        {
          name: '黄玮文',
          open_id: '',
        },
        {
          name: '黄卫宁',
          open_id: '',
        },
      ],
      problemDescription: '卡片联动测试',
      userTtid: ['123456'],
      attachments: null,
      apps: ['TT语音', '欢游'],
      appVersion: ['6.24.0'],
      osType: ['Android'],
      mobileType: ['小米10'],
      hasFeedback: null,
      feedbackPerson: 'admin',
      hasWorkingHours: null,
      severityLevel: null,
      functionType: ['首页', '扩列聊天', '小纸条'],
      firstLevelCategory: '首页',
      secondLevelCategory: '扩列聊天',
      thirdLevelCategory: '小纸条',
      reason: null,
      respondent: null,
      devProcessor: null,
      cause: null,
      hasClientRelease: null,
      changeLogs: [
        {
          time: '2023-11-20 23:19:02',
          content: '创建了工单',
          operator: 'admin',
        },
      ],
    },
  ];
  return ticketListData;
})();

export default [
  {
    url: '/basic-api/feedback/getTicketList',
    timeout: 100,
    method: 'post',
    response: ({ query }) => {
      const { page = 1, pageSize = 20 } = query;
      return resultPageSuccess(page, pageSize, ticketList);
    },
  },
  {
    url: '/basic-api/feedback/getCategoryList',
    timeout: 100,
    method: 'get',
    response: () => {
      return resultSuccess([
        {
          value: '首页',
          label: '首页',
          children: [
            {
              value: '搜索功能',
              label: '搜索功能',
              children: [
                {
                  value: '搜索 玩伴/房间/公会',
                  label: '搜索 玩伴/房间/公会',
                },
              ],
            },
            {
              value: '扩列聊天',
              label: '扩列聊天',
              children: [
                {
                  value: '小纸条',
                  label: '小纸条',
                },
                {
                  value: '扩列墙',
                  label: '扩列墙',
                },
              ],
            },
            {
              value: '音乐专区',
              label: '音乐专区',
              children: [
                {
                  value: '乐窝服务',
                  label: '乐窝服务',
                },
                {
                  value: '自定义个人认证标',
                  label: '自定义个人认证标',
                },
                {
                  value: '乐窝房间人数灌水',
                  label: '乐窝房间人数灌水',
                },
                {
                  value: '自定义投票',
                  label: '自定义投票',
                },
                {
                  value: '乐窝房间投票灌水',
                  label: '乐窝房间投票灌水',
                },
                {
                  value: '投票',
                  label: '投票',
                },
                {
                  value: '说唱房里面的玩法',
                  label: '说唱房里面的玩法',
                },
                {
                  value: 'AI说唱/外语',
                  label: 'AI说唱/外语',
                },
                {
                  value: '社群相关',
                  label: '社群相关',
                },
                {
                  value: '厂牌相册',
                  label: '厂牌相册',
                },
                {
                  value: '麦位排序',
                  label: '麦位排序',
                },
                {
                  value: '麦位名称',
                  label: '麦位名称',
                },
                {
                  value: '优质房内容识别提醒',
                  label: '优质房内容识别提醒',
                },
                {
                  value: '挂房听歌碎碎念',
                  label: '挂房听歌碎碎念',
                },
                {
                  value: '房间ktv服务',
                  label: '房间ktv服务',
                },
                {
                  value: '新挂房听歌',
                  label: '新挂房听歌',
                },
                {
                  value: '接歌抢唱',
                  label: '接歌抢唱',
                },
                {
                  value: '心跳服务，分别提供了ktv和麦可乐队的房间心跳服务',
                  label: '心跳服务，分别提供了ktv和麦可乐队的房间心跳服务',
                },
                {
                  value: '唱力/段位排行榜',
                  label: '唱力/段位排行榜',
                },
                {
                  value: '通用的排行榜服务',
                  label: '通用的排行榜服务',
                },
                {
                  value: '通用的学校服务',
                  label: '通用的学校服务',
                },
                {
                  value: '挂房听歌花花排行榜',
                  label: '挂房听歌花花排行榜',
                },
                {
                  value: 'ktv唱力/段位排行榜',
                  label: 'ktv唱力/段位排行榜',
                },
                {
                  value: '麦可乐队',
                  label: '麦可乐队',
                },
              ],
            },
            {
              value: '密室逃脱',
              label: '密室逃脱',
              children: [
                {
                  value: '谜境玩法',
                  label: '谜境玩法',
                },
              ],
            },
          ],
        },
        {
          value: '聊天页面',
          label: '聊天页面',
          children: [
            {
              value: '亲密任务',
              label: '亲密任务',
              children: [
                {
                  value: '印记',
                  label: '印记',
                },
              ],
            },
            {
              value: '挚友相关问题',
              label: '挚友相关问题',
              children: [
                {
                  value: '挚友值',
                  label: '挚友值',
                },
              ],
            },
            {
              value: '搜索功能',
              label: '搜索功能',
              children: [
                {
                  value: '搜索 玩伴/房间/公会',
                  label: '搜索 玩伴/房间/公会',
                },
              ],
            },
          ],
        },
        {
          value: '个人页面',
          label: '个人页面',
          children: [
            {
              value: '动态tab',
              label: '动态tab',
            },
            {
              value: '挚友相关问题',
              label: '挚友相关问题',
              children: [
                {
                  value: '挚友值',
                  label: '挚友值',
                },
              ],
            },
            {
              value: '个性装扮',
              label: '个性装扮',
              children: [
                {
                  value: '主页飘、头像框、坐骑',
                  label: '主页飘、头像框、坐骑',
                },
              ],
            },
            {
              value: '积分相关问题',
              label: '积分相关问题',
              children: [
                {
                  value: '积分兑换',
                  label: '积分兑换',
                },
              ],
            },
            {
              value: '个人资料',
              label: '个人资料',
              children: [
                {
                  value: '头像、昵称、性别、相册',
                  label: '头像、昵称、性别、相册',
                },
              ],
            },
            {
              value: '公会相关问题',
              label: '公会相关问题',
              children: [
                {
                  value: '公会解散',
                  label: '公会解散',
                },
              ],
            },
          ],
        },
        {
          value: '房间玩法',
          label: '房间玩法',
          children: [
            {
              value: '背包礼物',
              label: '背包礼物',
            },
            {
              value: '小游戏',
              label: '小游戏',
              children: [
                {
                  value: '桌球',
                  label: '桌球',
                },
                {
                  value: '五子棋',
                  label: '五子棋',
                },
                {
                  value: '大富翁',
                  label: '大富翁',
                },
                {
                  value: '语音猜题王',
                  label: '语音猜题王',
                },
                {
                  value: '牛牛大作战',
                  label: '牛牛大作战',
                },
                {
                  value: '飞行棋',
                  label: '飞行棋',
                },
                {
                  value: '呆鸭杀',
                  label: '呆鸭杀',
                },
                {
                  value: '狼人杀',
                  label: '狼人杀',
                },
              ],
            },
          ],
        },
        {
          value: '动态广场',
          label: '动态广场',
          children: [
            {
              value: '广场派对页',
              label: '广场派对页',
            },
            {
              value: '广场页',
              label: '广场页',
              children: [
                {
                  value: '推荐页',
                  label: '推荐页',
                },
                {
                  value: '关注页',
                  label: '关注页',
                },
              ],
            },
            {
              value: '话题页',
              label: '话题页',
              children: [
                {
                  value: '包括话题页、心情页、城市页）',
                  label: '包括话题页、心情页、城市页）',
                },
              ],
            },
            {
              value: '专区页',
              label: '专区页',
              children: [
                {
                  value: '说唱街区帖子、校园专区帖子、扩列专区帖子',
                  label: '说唱街区帖子、校园专区帖子、扩列专区帖子',
                },
              ],
            },
            {
              value: '发布器',
              label: '发布器',
              children: [
                {
                  value: '包括广场页、个人页、话题页',
                  label: '包括广场页、个人页、话题页',
                },
              ],
            },
            {
              value: '互动消息',
              label: '互动消息',
              children: [
                {
                  value: '动态详情页、评论区、互动消息',
                  label: '动态详情页、评论区、互动消息',
                },
              ],
            },
          ],
        },
        {
          value: '登录页面',
          label: '登录页面',
          children: [
            {
              value: '登录相关',
              label: '登录相关',
            },
          ],
        },
      ]);
    },
  },
] as MockMethod[];
