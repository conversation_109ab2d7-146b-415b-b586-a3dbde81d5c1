import { FaultParams, FaultListGetResultModel } from './model/improvementModel';
import { defHttp } from '@/utils/http/axios';

enum Api {
  // 故障列表
  getImprovementList = '/V2/rectify/selectRectify/',
}

// 获取故障列表
export const getImprovementList = (params) =>
  defHttp.post<FaultListGetResultModel>({
    url: Api.getImprovementList,
    params: { data: params },
  });

// // 创建故障
// export const createFault = (params) =>
//   defHttp.post({ url: Api.createFault, params: { data: params } });

// // 编辑故障
// export const editFault = (params) => defHttp.post({ url: Api.editFault, params: { data: params } });

// // 删除故障
// export const deleteFaultAPI = (params) =>
//   defHttp.post({ url: Api.deleteFaultAPI, params: { data: params } });
