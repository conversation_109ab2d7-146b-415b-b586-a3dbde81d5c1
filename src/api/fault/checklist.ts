import { defHttp } from '@/utils/http/axios';

enum Api {
  // 创建检核清单
  createCheckList = '/V2/checklist/createChecklist/',
  // 获取检核清单
  selectCheckList = '/V2/checklist/selectChecklist/',
  // 编辑检核清单
  editCheckList = '/V2/checklist/editChecklist/',
  // 删除检核清单
  deleteCheckList = '/V2/checklist/deleteChecklist/',

  // 创建检核结果
  createCheckResult = '/V2/checkresult/createCheckresult/',
  // 获取检核结果
  selectCheckResult = '/V2/checkresult/selectCheckresult/',
  // 编辑检核结果
  editCheckResult = '/V2/checkresult/editCheckresult/',
  // 删除检核结果
  deleteCheckResult = '/V2/checkresult/deleteCheckresult/',
}

// 创建检核清单
export const createCheckListAPI = (params) =>
  defHttp.post({
    url: Api.createCheckList,
    params: { data: params },
  });

// 获取检核清单
export const selectCheckListAPI = (params) =>
  defHttp.post({
    url: Api.selectCheckList,
    params: { data: params },
  });

// 编辑检核清单
export const editCheckListAPI = (params) =>
  defHttp.post({
    url: Api.editCheckList,
    params: { data: params },
  });
// 删除检核清单
export const deleteCheckListAPI = (params) =>
  defHttp.post({
    url: Api.deleteCheckList,
    params: { data: params },
  });

// 创建检核结果
export const createCheckResultAPI = (params) =>
  defHttp.post({
    url: Api.createCheckResult,
    params: { data: params },
  });

// 获取检核结果
export const selectCheckResultAPI = (params) =>
  defHttp.post({
    url: Api.selectCheckResult,
    params: { data: params },
  });

// 编辑检核结果
export const editCheckResultAPI = (params) =>
  defHttp.post({
    url: Api.editCheckResult,
    params: { data: params },
  });
// 删除检核结果
export const deleteCheckResultAPI = (params) =>
  defHttp.post({
    url: Api.deleteCheckResult,
    params: { data: params },
  });
