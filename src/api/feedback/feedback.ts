import { TicketListGetResultModel } from './model/feedbackModel';
import { defHttp } from '@/utils/http/axios';

enum Api {
  // 工单列表
  getTicketList = '/feedback/selectTicket/',

  // 创建工单
  createTicket = '/feedback/createTicket/',
  // 编辑工单
  editTicket = '/feedback/editTicket/',
  // 删除工单
  deleteTicketAPI = '/feedback/deleteTicket/',
  // 认领工单
  handleTicketAPI = '/feedback/handleTicket/',
  // 指派工单
  assignTicketAPI = '/feedback/assignTicket/',
  // 完成工单
  completeTicketAPI = '/feedback/completeTicket/',
  // 归档工单
  checkTicketAPI = '/feedback/checkTicket/',
  // 驳回工单
  rejectTicketAPI = '/feedback/rejectTicket/',
  // 加急工单
  urgeTicketAPI = '/feedback/urgeTicket/',
  // 功能列表
  CategoryList = '/ResponsiblePersons/categoryTree/',
  // 人员列表
  userList = '/user/auth/userlist/',

  // 请求dify
  difyWorkflowsRunAPI = '/dify/workflows/run',
}

// 获取工单列表
export const getTicketList = (params) =>
  defHttp.post<TicketListGetResultModel>({
    url: Api.getTicketList,
    params: { data: params },
  });

// 创建工单
export const createTicket = (params) =>
  defHttp.post({ url: Api.createTicket, params: { data: params } });

// 编辑工单
export const editTicket = (params) =>
  defHttp.post({ url: Api.editTicket, params: { data: params } });

// 删除工单
export const deleteTicketAPI = (params) =>
  defHttp.post({ url: Api.deleteTicketAPI, params: { data: params } });

// 认领工单
export const handleTicketAPI = (params) =>
  defHttp.post({ url: Api.handleTicketAPI, params: { data: params } });

// 加急工单
export const urgeTicketAPI = (params) =>
  defHttp.post({ url: Api.urgeTicketAPI, params: { data: params } });

// 指派工单
export const assignTicketAPI = (params) =>
  defHttp.post({ url: Api.assignTicketAPI, params: { data: params } });

// 完成工单
export const completeTicketAPI = (params) =>
  defHttp.post({ url: Api.completeTicketAPI, params: { data: params } });

// 归档工单
export const checkTicketAPI = (params) =>
  defHttp.post({ url: Api.checkTicketAPI, params: { data: params } });

// 驳回工单
export const rejectTicketAPI = (params) =>
  defHttp.post({ url: Api.rejectTicketAPI, params: { data: params } });

// 功能列表
export const getCategoryList = () => defHttp.get({ url: Api.CategoryList });
// 用户列表
export const getUserList = () => defHttp.get({ url: Api.userList });

// 请求dify工作流
export const difyWorkflowsRunAPI_Extract_feedback_info = (params) =>
  defHttp.post({ url: Api.difyWorkflowsRunAPI + '/extract_feedback_info/', params: { ...params } });
