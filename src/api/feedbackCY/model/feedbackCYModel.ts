import { BasicPageParams, BasicFetchResult } from '@/api/model/baseModel';

export type TicketParams = BasicPageParams & {
  account?: string;
  nickname?: string;
  [key: string]: any;
};

export type RoleParams = {
  roleName?: string;
  status?: string;
};

export type RolePageParams = BasicPageParams & RoleParams;

export type DeptParams = {
  deptName?: string;
  status?: string;
};

export type MenuParams = {
  menuName?: string;
  status?: string;
};

export interface TicketListItem {
  problemDescription: string;
  apps: Array<string>;
  attachments: Array<string>;
  // severityLevel: string;
  appVersion: Array<string>;
  functionType: Array<string>;
  userTtid: Array<string>;
  stage: string;
  enterTime: string;
  ticketID: string;
}

export interface DeptListItem {
  id: string;
  orderNo: string;
  createTime: string;
  remark: string;
  status: number;
}

export interface MenuListItem {
  id: string;
  orderNo: string;
  createTime: string;
  status: number;
  icon: string;
  component: string;
  permission: string;
}

export interface RoleListItem {
  id: string;
  roleName: string;
  roleValue: string;
  status: number;
  orderNo: string;
  createTime: string;
}

/**
 * @description: Request list return value
 */
export type TicketListGetResultModel = BasicFetchResult<TicketListItem>;

export type DeptListGetResultModel = BasicFetchResult<DeptListItem>;

export type MenuListGetResultModel = BasicFetchResult<MenuListItem>;

export type RolePageListGetResultModel = BasicFetchResult<RoleListItem>;

export type RoleListGetResultModel = RoleListItem[];
