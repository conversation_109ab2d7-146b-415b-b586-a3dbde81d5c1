// 调用客服中台的接口
import { otherHttp } from '@/utils/http/axios';
import { isDevMode } from '@/utils/env';

const devURL = 'http://121.36.41.9:9010/api/kf-feed-back/info/record';
const prodURL = 'https://kf-feed-back-urrc.52tt.com/api/kf-feed-back/info/record';

// 获取工单列表
export const getFeedbackRecordAPI = (params) =>
  otherHttp.post(
    {
      url: isDevMode() ? devURL : prodURL,
      params,
    },
    {
      // isTransformResponse: false,
    },
  );
