import { ProblemParams, ProblemListGetResultModel } from './model/improvementModel';
import { defHttp } from '@/utils/http/axios';

enum Api {
  // 故障列表
  getImprovementList = '/V2/rectify/selectRectify/',

  // // 创建故障
  // createProblem = '/problem/createReport/',
  // // 编辑整改措施
  editImprovement = '/V2/rectify/editRectify/',
  // // 删除整改措施
  deleteImprovement = '/V2/rectify/deleteRectify/',
}

// 获取故障列表
export const getImprovementListAPI = (params) =>
  defHttp.post<ProblemListGetResultModel>({
    url: Api.getImprovementList,
    params: { data: params },
  });

// // 创建故障
// export const createProblem = (params) =>
//   defHttp.post({ url: Api.createProblem, params: { data: params } });

// // 编辑整改措施
export const editImprovementAPI = (params) =>
  defHttp.post({ url: Api.editImprovement, params: { data: params } });

// // 删除整改措施
export const deleteImprovementAPI = (params) =>
  defHttp.post({ url: Api.deleteImprovement, params: { data: params } });
