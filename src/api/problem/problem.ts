import { ProblemListGetResultModel } from './model/problemModel';
import { defHttp } from '@/utils/http/axios';

enum Api {
  // 问题列表
  getProblemList = '/V2/fault/selectFault/',

  // 统计数据
  getDashboardDataAPI = '/V2/problem/getDashboardData/',
  // 创建问题，获取故障ID
  createProblem = '/V2/fault/createFault/',
  // 编辑问题
  editProblem = '/V2/fault/editFault/',
  // 删除问题
  deleteProblemAPI = '/V2/fault/deleteFault/',
}

// 获取问题列表
export const getProblemList = (params) =>
  defHttp.post<ProblemListGetResultModel>({
    url: Api.getProblemList,
    params: { data: params },
  });

// 统计数据
export const getDashboardDataAPI = (params) =>
  defHttp.post({
    url: Api.getDashboardDataAPI,
    params: { data: params },
  });

// 创建问题
export const createProblem = (params) =>
  // defHttp.post({ url: Api.createProblem, params: { data: params } });
  defHttp.post({ url: Api.createProblem, params: { data: { ...params } } });

// 编辑问题
export const editProblem = (params) =>
  defHttp.post({ url: Api.editProblem, params: { data: params } });

// 删除问题
export const deleteProblemAPI = (params) =>
  defHttp.post({ url: Api.deleteProblemAPI, params: { data: params } });
